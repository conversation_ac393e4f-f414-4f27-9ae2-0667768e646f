/*=====================================================================
 * 文件：transient.h
 *
 * 描述：暂态录波数据处理模块头文件
 *
 * 作者：系统生成			2025年1月
 *
 * 修改记录：
 =====================================================================*/

#ifndef TRANSIENT_H
#define TRANSIENT_H

#include <fstream>
#include <string>
#include <vector>
#include <cstdint>
#include <memory>

struct AnalogChannel;
struct DigitalChannel;
struct SamplingRate;

/**
 * COMTRADE配置信息类
 */
class ComtradeConfig {
public:
    std::string station_name;       // 厂站名称
    std::string recorder_id;        // 录波器编号
    std::vector<AnalogChannel> analog_channels;  // 模拟量通道
    std::vector<DigitalChannel> digital_channels; // 数字量通道
    double frequency;               // 系统频率
    std::vector<SamplingRate> sampling_rates;     // 采样率信息
    std::string start_time;         // 开始时间 (格式: MM/DD/YY,HH:MM:SS.ssssss)
    std::string end_time;           // 结束时间
    
    // 获取总通道数
    size_t total_channels() const {
        return analog_channels.size() + digital_channels.size();
    }
    
    // 获取模拟量通道数
    size_t analog_count() const {
        return analog_channels.size();
    }
    
    // 获取数字量通道数
    size_t digital_count() const {
        return digital_channels.size();
    }
};

/**
 * 模拟量通道信息
 */
struct AnalogChannel {
    int index;              // 通道索引
    std::string name;       // 通道名称
    std::string phase;      // 相位标识
    std::string element;    // 关联元素
    std::string unit;       // 单位
    double factor_a;        // 转换因子A
    double factor_b;        // 转换因子B
    int offset_time;        // 时间偏移
    int min_sample;         // 最小采样值
    int max_sample;         // 最大采样值
};

/**
 * 数字量通道信息
 */
struct DigitalChannel {
    int index;              // 通道索引
    std::string name;       // 通道名称
    int state;              // 状态 (0或1)
};

/**
 * 采样率信息
 */
struct SamplingRate {
    double rate;            // 采样率 (Hz)
    int points;             // 该采样率下的点数
};

/**
 * 采样点数据
 */
struct SamplePoint {
    int index;                      // 采样点索引
    int64_t time;                   // 时间偏移 (微秒)
    std::vector<int> analog_values;  // 模拟量值
    std::vector<int> digital_values; // 数字量值
};

/**
 * 暂态分析器配置结构
 */
struct TransientConfig {
    double sampling_rate;           // 采样率 (Hz)
    double trigger_threshold;       // 触发阈值
    int pre_trigger_samples;        // 触发前采样点数
    int post_trigger_samples;       // 触发后采样点数
    int channel_count;              // 通道数
};

/**
 * 暂态录波分析器类
 * 负责暂态事件检测和录波文件生成
 */
class TransientAnalyzer {
public:
    /**
     * 构造函数
     */
    TransientAnalyzer();

    /**
     * 析构函数
     */
    ~TransientAnalyzer();

    /**
     * 初始化分析器
     * @return 成功返回true，失败返回false
     */
    bool Init();

    /**
     * 退出并清理资源
     */
    void Exit();

    /**
     * 启动分析器
     * @return 成功返回true，失败返回false
     */
    bool Start();

    /**
     * 停止分析器
     */
    void Stop();

    /**
     * 处理实时数据
     * @param data 采样数据
     * @param timestamp 时间戳（微秒）
     * @return 成功返回true，失败返回false
     */
    bool ProcessData(const std::vector<float>& data, int64_t timestamp);

    /**
     * 获取运行状态
     * @return 运行中返回true，否则返回false
     */
    bool IsRunning() const { return m_bRunning; }

    /**
     * 获取初始化状态
     * @return 已初始化返回true，否则返回false
     */
    bool IsInitialized() const { return m_bInitialized; }

private:
    // 初始化方法
    void InitConfig();
    void InitDataBuffer();
    void InitAlgorithmParams();
    void ClearDataBuffer();

    // 数据处理方法
    bool PreprocessData(const std::vector<float>& data);
    bool DetectTransient(const std::vector<float>& data, int64_t timestamp);
    void TriggerRecording(const std::vector<float>& data, int64_t timestamp);

    // COMTRADE文件生成方法
    ComtradeConfig CreateComtradeConfig(int64_t timestamp);
    std::vector<SamplePoint> PrepareRecordingData(int64_t timestamp);
    std::string GenerateFilename(int64_t timestamp);
    std::string FormatTimestamp(int64_t timestamp);

private:
    bool m_bInitialized;                    // 初始化标志
    bool m_bRunning;                        // 运行状态标志
    TransientConfig m_config;               // 配置参数
    std::vector<float> m_dataBuffer;        // 数据缓冲区
    size_t m_bufferIndex;                   // 缓冲区索引
    int64_t m_lastTriggerTime;              // 上次触发时间
    int64_t m_triggerCooldown;              // 触发冷却时间
};

/**
 * COMTRADE文件写入器
 */
class ComtradeWriter {
public:
    /**
     * 写入CFG配置文件
     * @param filename 文件名
     * @param config 配置信息
     * @return 成功返回true，失败返回false
     */
    static bool write_cfg(const std::string& filename, const ComtradeConfig& config);

    /**
     * 写入ASCII格式的DAT文件
     * @param filename 文件名
     * @param config 配置信息（用于验证通道数量）
     * @param samples 采样点数据
     * @return 成功返回true，失败返回false
     */
    static bool write_dat_ascii(const std::string& filename, const ComtradeConfig& config,
                               const std::vector<SamplePoint>& samples);

    /**
     * 写入单个采样点到已打开的ASCII格式DAT文件
     * @param file 已打开的文件流
     * @param sample 采样点数据
     * @return 成功返回true，失败返回false
     */
    static bool write_sample_ascii(std::ofstream& file, const SamplePoint& sample);
};

#endif