/*=====================================================================
 * 文件：transient.cpp
 *
 * 描述：暂态录波数据处理模块实现
 *
 * 作者：系统生成			2025年1月
 *
 * 修改记录：
 =====================================================================*/

#include "transient.h"
#include "public_struct.h"
#include <iomanip>
#include <sstream>
#include <stdexcept>
#include <iostream>
#include <sys/types.h>
#include <cmath>
#include <algorithm>

//=============================================================================
// TransientConfig 静态成员定义
//=============================================================================

// 通道名称：三相电压 + 三相电流
const char* TransientConfig::CHANNEL_NAMES[CHANNEL_COUNT] = {
    "UA", "UB", "UC",           // 三相电压
    "IA", "IB", "IC"            // 三相电流
};

// 通道相位标识
const char* TransientConfig::CHANNEL_PHASES[CHANNEL_COUNT] = {
    "A", "B", "C",              // 电压相位
    "A", "B", "C"               // 电流相位
};

// 通道单位
const char* TransientConfig::CHANNEL_UNITS[CHANNEL_COUNT] = {
    "V", "V", "V",              // 电压单位
    "A", "A", "A"               // 电流单位
};

//=============================================================================
// TransientAnalyzer 类实现
//=============================================================================

TransientAnalyzer::TransientAnalyzer()
    : m_bInitialized(false)
    , m_bRunning(false)
{
    IEC_LOG_RECORD(eRunType, "TransientAnalyzer constructor called.");
}

TransientAnalyzer::~TransientAnalyzer()
{
    Exit();
    IEC_LOG_RECORD(eRunType, "TransientAnalyzer destructor called.");
}

bool TransientAnalyzer::Init()
{
    if (m_bInitialized) {
        IEC_LOG_RECORD(eWarnType, "TransientAnalyzer already initialized.");
        return true;
    }

    try {
        // 初始化配置参数
        InitConfig();

        // 初始化数据缓冲区
        InitDataBuffer();

        // 初始化算法参数
        InitAlgorithmParams();

        m_bInitialized = true;
        IEC_LOG_RECORD(eRunType, "TransientAnalyzer initialized successfully.");
        return true;
    }
    catch (const std::exception& e) {
        IEC_LOG_RECORD(eErrType, "TransientAnalyzer init failed: %s", e.what());
        return false;
    }
}

void TransientAnalyzer::Exit()
{
    if (!m_bInitialized) {
        return;
    }

    Stop();

    // 清理资源
    ClearDataBuffer();

    m_bInitialized = false;
    IEC_LOG_RECORD(eRunType, "TransientAnalyzer exited.");
}

bool TransientAnalyzer::Start()
{
    if (!m_bInitialized) {
        IEC_LOG_RECORD(eErrType, "TransientAnalyzer not initialized, cannot start.");
        return false;
    }

    if (m_bRunning) {
        IEC_LOG_RECORD(eWarnType, "TransientAnalyzer already running.");
        return true;
    }

    m_bRunning = true;
    IEC_LOG_RECORD(eRunType, "TransientAnalyzer started.");
    return true;
}

void TransientAnalyzer::Stop()
{
    if (!m_bRunning) {
        return;
    }

    m_bRunning = false;
    IEC_LOG_RECORD(eRunType, "TransientAnalyzer stopped.");
}

bool TransientAnalyzer::ProcessData(const std::vector<float>& data, int64_t timestamp)
{
    if (!m_bRunning) {
        IEC_LOG_RECORD(eWarnType, "TransientAnalyzer not running, data processing skipped.");
        return false;
    }

    try {
        // 数据预处理
        if (!PreprocessData(data)) {
            IEC_LOG_RECORD(eErrType, "Data preprocessing failed.");
            return false;
        }

        // 暂态检测
        if (DetectTransient(data, timestamp)) {
            // 触发录波
            TriggerRecording(data, timestamp);
        }

        return true;
    }
    catch (const std::exception& e) {
        IEC_LOG_RECORD(eErrType, "ProcessData failed: %s", e.what());
        return false;
    }
}

//=============================================================================
// 私有方法实现
//=============================================================================

void TransientAnalyzer::InitConfig()
{
    // 设置默认配置参数
    m_config.sampling_rate = 12800.0;          // 采样率 Hz
    m_config.voltage_threshold = 380.0 * 1.2;  // 电压触发阈值 (额定电压的1.2倍)
    m_config.current_threshold = 100.0 * 1.5;  // 电流触发阈值 (额定电流的1.5倍)
    m_config.pre_trigger_samples = 1024;       // 触发前采样点数
    m_config.post_trigger_samples = 2048;      // 触发后采样点数

    IEC_LOG_RECORD(eRunType, "TransientAnalyzer config initialized: SR=%.1f, V_Threshold=%.1fV, I_Threshold=%.1fA",
                   m_config.sampling_rate, m_config.voltage_threshold, m_config.current_threshold);
                   
}

void TransientAnalyzer::InitDataBuffer()
{
    size_t buffer_size = m_config.pre_trigger_samples + m_config.post_trigger_samples;
    m_dataBuffer.resize(buffer_size * TransientConfig::CHANNEL_COUNT);
    m_bufferIndex = 0;

    IEC_LOG_RECORD(eRunType, "Data buffer initialized: size=%zu, channels=%d",
                   buffer_size, TransientConfig::CHANNEL_COUNT);
}

void TransientAnalyzer::InitAlgorithmParams()
{
    // 初始化算法相关参数
    m_lastTriggerTime = 0;
    m_triggerCooldown = 1000000; // 1秒冷却时间（微秒）

    IEC_LOG_RECORD(eRunType, "Algorithm parameters initialized.");
}

void TransientAnalyzer::ClearDataBuffer()
{
    m_dataBuffer.clear();
    m_bufferIndex = 0;

    IEC_LOG_RECORD(eRunType, "Data buffer cleared.");
}

bool TransientAnalyzer::PreprocessData(const std::vector<float>& data)
{
    if (data.size() != TransientConfig::CHANNEL_COUNT) {
        IEC_LOG_RECORD(eErrType, "Data size mismatch: expected %d, got %zu",
                       TransientConfig::CHANNEL_COUNT, data.size());
        return false;
    }

    // 数据有效性检查
    for (size_t i = 0; i < data.size(); ++i) {
        if (std::isnan(data[i]) || std::isinf(data[i])) {
            IEC_LOG_RECORD(eWarnType, "Invalid data detected at channel %zu (%s)",
                           i, TransientConfig::CHANNEL_NAMES[i]);
            return false;
        }
    }

    return true;
}

bool TransientAnalyzer::DetectTransient(const std::vector<float>& data, int64_t timestamp)
{
    // 检查冷却时间
    if (timestamp - m_lastTriggerTime <= m_triggerCooldown) {
        return false;
    }

    // 分别检测电压和电流通道
    for (size_t i = 0; i < data.size(); ++i) {
        float threshold;
        const char* type;

        if (i < 3) {  // 电压通道 (UA, UB, UC)
            threshold = m_config.voltage_threshold;
            type = "voltage";
        } else {      // 电流通道 (IA, IB, IC)
            threshold = m_config.current_threshold;
            type = "current";
        }

        if (std::abs(data[i]) > threshold) {
            IEC_LOG_RECORD(eRunType, "Transient detected on %s channel %s: value=%.3f (threshold=%.3f)",
                           type, TransientConfig::CHANNEL_NAMES[i], data[i], threshold);
            return true;
        }
    }

    return false;
}

void TransientAnalyzer::TriggerRecording(const std::vector<float>& data, int64_t timestamp)
{
    m_lastTriggerTime = timestamp;

    // 创建COMTRADE配置
    ComtradeConfig config = CreateComtradeConfig(timestamp);

    // 生成文件名
    std::string filename = GenerateFilename(timestamp);

    // 写入配置文件
    if (ComtradeWriter::write_cfg(filename + ".cfg", config)) {
        IEC_LOG_RECORD(eRunType, "COMTRADE config file created: %s.cfg", filename.c_str());
    } else {
        IEC_LOG_RECORD(eErrType, "Failed to create COMTRADE config file: %s.cfg", filename.c_str());
    }

    // 准备采样数据
    std::vector<SamplePoint> samples = PrepareRecordingData(timestamp);

    // 写入数据文件
    if (ComtradeWriter::write_dat_ascii(filename + ".dat", config, samples)) {
        IEC_LOG_RECORD(eRunType, "COMTRADE data file created: %s.dat", filename.c_str());
    } else {
        IEC_LOG_RECORD(eErrType, "Failed to create COMTRADE data file: %s.dat", filename.c_str());
    }
}

ComtradeConfig TransientAnalyzer::CreateComtradeConfig(int64_t timestamp)
{
    ComtradeConfig config;

    config.station_name = "DTAnalyzer_Station";
    config.recorder_id = "DTAnalyzer_001";
    config.frequency = 50.0;

    // 创建6个模拟量通道：三相电压 + 三相电流
    for (int i = 0; i < TransientConfig::CHANNEL_COUNT; ++i) {
        AnalogChannel channel;
        channel.index = i + 1;
        channel.name = TransientConfig::CHANNEL_NAMES[i];
        channel.phase = TransientConfig::CHANNEL_PHASES[i];
        channel.unit = TransientConfig::CHANNEL_UNITS[i];

        // 根据通道类型设置元素类型和转换因子
        if (i < 3) {  // 电压通道
            channel.element = "V";
            channel.factor_a = 0.1;    // 电压转换因子 (假设ADC满量程对应1000V)
            channel.factor_b = 0.0;
            channel.min_sample = -32768;
            channel.max_sample = 32767;
        } else {      // 电流通道
            channel.element = "I";
            channel.factor_a = 0.01;   // 电流转换因子 (假设ADC满量程对应1000A)
            channel.factor_b = 0.0;
            channel.min_sample = -32768;
            channel.max_sample = 32767;
        }

        channel.offset_time = 0;
        config.analog_channels.push_back(channel);
    }

    // 设置采样率
    SamplingRate rate;
    rate.rate = m_config.sampling_rate;
    rate.points = m_config.pre_trigger_samples + m_config.post_trigger_samples;
    config.sampling_rates.push_back(rate);

    // 设置时间戳
    config.start_time = FormatTimestamp(timestamp - m_config.pre_trigger_samples * 1000000 / m_config.sampling_rate);
    config.end_time = FormatTimestamp(timestamp + m_config.post_trigger_samples * 1000000 / m_config.sampling_rate);

    return config;
}

std::vector<SamplePoint> TransientAnalyzer::PrepareRecordingData(int64_t timestamp)
{
    std::vector<SamplePoint> samples;

    size_t total_samples = m_config.pre_trigger_samples + m_config.post_trigger_samples;
    samples.reserve(total_samples);

    for (size_t i = 0; i < total_samples; ++i) {
        SamplePoint sample;
        sample.index = static_cast<int>(i + 1);
        sample.time = static_cast<int64_t>(i * 1000000 / m_config.sampling_rate);

        // 从缓冲区获取数据（这里简化处理，实际应该从环形缓冲区获取）
        sample.analog_values.resize(TransientConfig::CHANNEL_COUNT);

        for (int ch = 0; ch < TransientConfig::CHANNEL_COUNT; ++ch) {
            if (ch < 3) {  // 电压通道 (UA, UB, UC)
                // 模拟三相电压数据 (相位差120度)
                double phase_offset = ch * 2.0 * M_PI / 3.0;  // 120度相位差
                double voltage = 380.0 * sqrt(2) * sin(2 * M_PI * 50 * i / m_config.sampling_rate + phase_offset);
                sample.analog_values[ch] = static_cast<int>(voltage / 0.1);  // 根据转换因子转换
            } else {       // 电流通道 (IA, IB, IC)
                // 模拟三相电流数据 (相位差120度，滞后电压30度)
                double phase_offset = (ch - 3) * 2.0 * M_PI / 3.0 - M_PI / 6.0;  // 120度相位差 + 30度滞后
                double current = 100.0 * sqrt(2) * sin(2 * M_PI * 50 * i / m_config.sampling_rate + phase_offset);
                sample.analog_values[ch] = static_cast<int>(current / 0.01);  // 根据转换因子转换
            }
        }

        samples.push_back(sample);
    }

    return samples;
}

std::string TransientAnalyzer::GenerateFilename(int64_t timestamp)
{
    time_t t = timestamp / 1000000;
    struct tm* tm_info = localtime(&t);

    char buffer[256];
    snprintf(buffer, sizeof(buffer), "%s/TRANS_%04d%02d%02d_%02d%02d%02d",
             COMTRADE_PATH,
             tm_info->tm_year + 1900,
             tm_info->tm_mon + 1,
             tm_info->tm_mday,
             tm_info->tm_hour,
             tm_info->tm_min,
             tm_info->tm_sec);

    return std::string(buffer);
}

std::string TransientAnalyzer::FormatTimestamp(int64_t timestamp)
{
    time_t t = timestamp / 1000000;
    int microseconds = timestamp % 1000000;
    struct tm* tm_info = localtime(&t);

    char buffer[64];
    snprintf(buffer, sizeof(buffer), "%02d/%02d/%02d,%02d:%02d:%02d.%06d",
             tm_info->tm_mon + 1,
             tm_info->tm_mday,
             tm_info->tm_year % 100,
             tm_info->tm_hour,
             tm_info->tm_min,
             tm_info->tm_sec,
             microseconds);

    return std::string(buffer);
}

//=============================================================================
// ComtradeWriter 类实现（保持原有功能）
//=============================================================================

bool ComtradeWriter::write_cfg(const std::string& filename, const ComtradeConfig& config) {
    try {
        std::ofstream file(filename);
        if (!file.is_open()) {
            return false;
        }
        
        // 写入厂站名称和录波器编号
        file << config.station_name << "," << config.recorder_id << "\n";
        
        // 写入通道数量信息（仅模拟量通道）
        file << config.total_channels() << ","
             << config.analog_count() << "A,"
             << "0D\n";  // 数字量通道数固定为0

        // 写入模拟量通道信息（三相电压+三相电流）
        for (const auto& analog : config.analog_channels) {
            file << analog.index << ","
                 << analog.name << ","
                 << analog.phase << ","
                 << analog.element << ","
                 << analog.unit << ","
                 << std::fixed << std::setprecision(6) << analog.factor_a << ","
                 << std::fixed << std::setprecision(6) << analog.factor_b << ","
                 << analog.offset_time << ","
                 << analog.min_sample << ","
                 << analog.max_sample << "\n";
        }
        
        // 写入频率信息
        file << std::fixed << std::setprecision(1) << config.frequency << "\n";
        
        // 写入采样率数量
        file << config.sampling_rates.size() << "\n";

        
        
        // 写入各采样率信息
        for (const auto& rate : config.sampling_rates) {
            file << std::fixed << std::setprecision(1) << rate.rate << ","
                 << rate.points << "\n";
        }
        
        // 写入开始时间和结束时间
        file << config.start_time << "\n";
        file << config.end_time << "\n";

        
        
        // 写入文件类型(固定为ASCII)
        file << "ASCII\n";
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool ComtradeWriter::write_sample_ascii(std::ofstream& file, const SamplePoint& sample) {
    if (!file.is_open()) {
        return false;
    }

    try {
        // 写入序号和时间
        file << sample.index << "," << sample.time;

        // 写入模拟量数据（三相电压+三相电流）
        for (int value : sample.analog_values) {
            file << "," << value;
        }

        // 不写入数字量数据（已取消数字通道）

        file << "\n";
        return true;
    }
    catch (...) {
        return false;
    }
}

bool ComtradeWriter::write_dat_ascii(const std::string& filename, const ComtradeConfig& config, 
                                    const std::vector<SamplePoint>& samples) {
    try {
        std::ofstream file(filename);
        if (!file.is_open()) {
            return false;
        }
        
        // 验证采样点数据与配置是否匹配
        for (const auto& sample : samples) {
            if (sample.analog_values.size() != config.analog_count()) {
                IEC_LOG_RECORD(eErrType, "Sample data size mismatch: expected %zu analog channels, got %zu",
                               config.analog_count(), sample.analog_values.size());
                return false; // 采样点数据与配置的通道数量不匹配
            }
        }
        
        // 写入所有采样点
        for (const auto& sample : samples) {
            if (!write_sample_ascii(file, sample)) {
                return false;
            }
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}
    