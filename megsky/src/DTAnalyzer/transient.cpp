#include "transient.h"
#include <iomanip>
#include <sstream>
#include <stdexcept>
#include <iostream>
#include <sys/types.h>
#include"public_struct.h"



bool ComtradeWriter::write_cfg(const std::string& filename, const ComtradeConfig& config) {
    try {
        std::ofstream file(filename);
        if (!file.is_open()) {
            return false;
        }
        
        // 写入厂站名称和录波器编号
        file << config.station_name << "," << config.recorder_id << "\n";
        
        // 写入通道数量信息
        file << config.total_channels() << "," 
             << config.analog_count() << "A," 
             << config.digital_count() << "D\n";
        
        // 写入模拟量通道信息
        for (const auto& analog : config.analog_channels) {
            file << analog.index << ","
                 << analog.name << ","
                 << analog.phase << ","
                 << analog.element << ","
                 << analog.unit << ","
                 << std::fixed << std::setprecision(6) << analog.factor_a << ","
                 << std::fixed << std::setprecision(6) << analog.factor_b << ","
                 << analog.offset_time << ","
                 << analog.min_sample << ","
                 << analog.max_sample << "\n";
        }
        
        // 写入数字量通道信息
        for (const auto& digital : config.digital_channels) {
            file << digital.index << ","
                 << digital.name << ","
                 << digital.state << "\n";
        }
        
        // 写入频率信息
        file << std::fixed << std::setprecision(1) << config.frequency << "\n";
        
        // 写入采样率数量
        file << config.sampling_rates.size() << "\n";

        
        
        // 写入各采样率信息
        for (const auto& rate : config.sampling_rates) {
            file << std::fixed << std::setprecision(1) << rate.rate << ","
                 << rate.points << "\n";
        }
        
        // 写入开始时间和结束时间
        file << config.start_time << "\n";
        file << config.end_time << "\n";

        
        
        // 写入文件类型(固定为ASCII)
        file << "ASCII\n";
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool ComtradeWriter::write_sample_ascii(std::ofstream& file, const SamplePoint& sample) {
    if (!file.is_open()) {
        return false;
    }
    
    try {
        // 写入序号和时间
        file << sample.index << "," << sample.time;
        
        // 写入模拟量数据
        for (int value : sample.analog_values) {
            file << "," << value;
        }
        
        // 写入数字量数据
        for (int value : sample.digital_values) {
            file << "," << value;
        }
        
        file << "\n";
        return true;
    }
    catch (...) {
        return false;
    }
}

bool ComtradeWriter::write_dat_ascii(const std::string& filename, const ComtradeConfig& config, 
                                    const std::vector<SamplePoint>& samples) {
    try {
        std::ofstream file(filename);
        if (!file.is_open()) {
            return false;
        }
        
        // 验证采样点数据与配置是否匹配
        for (const auto& sample : samples) {
            if (sample.analog_values.size() != config.analog_count() ||
                sample.digital_values.size() != config.digital_count()) {
                return false; // 采样点数据与配置的通道数量不匹配
            }
        }
        
        // 写入所有采样点
        for (const auto& sample : samples) {
            if (!write_sample_ascii(file, sample)) {
                return false;
            }
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}
    