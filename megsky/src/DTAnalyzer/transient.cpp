/*=====================================================================
 * 文件：transient.cpp
 *
 * 描述：暂态录波数据处理模块实现
 *
 * 作者：系统生成			2025年1月
 *
 * 修改记录：
 =====================================================================*/

#include "transient.h"
#include "public_struct.h"
#include <iomanip>
#include <sstream>
#include <stdexcept>
#include <iostream>
#include <sys/types.h>
#include <cmath>
#include <algorithm>

//=============================================================================
// TransientAnalyzer 类实现
//=============================================================================

TransientAnalyzer::TransientAnalyzer()
    : m_bInitialized(false)
    , m_bRunning(false)
{
    IEC_LOG_RECORD(eRunType, "TransientAnalyzer constructor called.");
}

TransientAnalyzer::~TransientAnalyzer()
{
    Exit();
    IEC_LOG_RECORD(eRunType, "TransientAnalyzer destructor called.");
}

bool TransientAnalyzer::Init()
{
    if (m_bInitialized) {
        IEC_LOG_RECORD(eWarnType, "TransientAnalyzer already initialized.");
        return true;
    }

    try {
        // 初始化配置参数
        InitConfig();

        // 初始化数据缓冲区
        InitDataBuffer();

        // 初始化算法参数
        InitAlgorithmParams();

        m_bInitialized = true;
        IEC_LOG_RECORD(eRunType, "TransientAnalyzer initialized successfully.");
        return true;
    }
    catch (const std::exception& e) {
        IEC_LOG_RECORD(eErrType, "TransientAnalyzer init failed: %s", e.what());
        return false;
    }
}

void TransientAnalyzer::Exit()
{
    if (!m_bInitialized) {
        return;
    }

    Stop();

    // 清理资源
    ClearDataBuffer();

    m_bInitialized = false;
    IEC_LOG_RECORD(eRunType, "TransientAnalyzer exited.");
}

bool TransientAnalyzer::Start()
{
    if (!m_bInitialized) {
        IEC_LOG_RECORD(eErrType, "TransientAnalyzer not initialized, cannot start.");
        return false;
    }

    if (m_bRunning) {
        IEC_LOG_RECORD(eWarnType, "TransientAnalyzer already running.");
        return true;
    }

    m_bRunning = true;
    IEC_LOG_RECORD(eRunType, "TransientAnalyzer started.");
    return true;
}

void TransientAnalyzer::Stop()
{
    if (!m_bRunning) {
        return;
    }

    m_bRunning = false;
    IEC_LOG_RECORD(eRunType, "TransientAnalyzer stopped.");
}

bool TransientAnalyzer::ProcessData(const std::vector<float>& data, int64_t timestamp)
{
    if (!m_bRunning) {
        IEC_LOG_RECORD(eWarnType, "TransientAnalyzer not running, data processing skipped.");
        return false;
    }

    try {
        // 数据预处理
        if (!PreprocessData(data)) {
            IEC_LOG_RECORD(eErrType, "Data preprocessing failed.");
            return false;
        }

        // 暂态检测
        if (DetectTransient(data, timestamp)) {
            // 触发录波
            TriggerRecording(data, timestamp);
        }

        return true;
    }
    catch (const std::exception& e) {
        IEC_LOG_RECORD(eErrType, "ProcessData failed: %s", e.what());
        return false;
    }
}

//=============================================================================
// 私有方法实现
//=============================================================================

void TransientAnalyzer::InitConfig()
{
    // 设置默认配置参数
    m_config.sampling_rate = 12800.0;  // 采样率 Hz
    m_config.trigger_threshold = 1.2;  // 触发阈值
    m_config.pre_trigger_samples = 1024;  // 触发前采样点数
    m_config.post_trigger_samples = 2048; // 触发后采样点数
    m_config.channel_count = 8;        // 通道数

    IEC_LOG_RECORD(eRunType, "TransientAnalyzer config initialized: SR=%.1f, Threshold=%.2f",
                   m_config.sampling_rate, m_config.trigger_threshold);
}

void TransientAnalyzer::InitDataBuffer()
{
    size_t buffer_size = m_config.pre_trigger_samples + m_config.post_trigger_samples;
    m_dataBuffer.resize(buffer_size * m_config.channel_count);
    m_bufferIndex = 0;

    IEC_LOG_RECORD(eRunType, "Data buffer initialized: size=%zu", buffer_size);
}

void TransientAnalyzer::InitAlgorithmParams()
{
    // 初始化算法相关参数
    m_lastTriggerTime = 0;
    m_triggerCooldown = 1000000; // 1秒冷却时间（微秒）

    IEC_LOG_RECORD(eRunType, "Algorithm parameters initialized.");
}

void TransientAnalyzer::ClearDataBuffer()
{
    m_dataBuffer.clear();
    m_bufferIndex = 0;

    IEC_LOG_RECORD(eRunType, "Data buffer cleared.");
}

bool TransientAnalyzer::PreprocessData(const std::vector<float>& data)
{
    if (data.size() != m_config.channel_count) {
        IEC_LOG_RECORD(eErrType, "Data size mismatch: expected %d, got %zu",
                       m_config.channel_count, data.size());
        return false;
    }

    // 数据有效性检查
    for (size_t i = 0; i < data.size(); ++i) {
        if (std::isnan(data[i]) || std::isinf(data[i])) {
            IEC_LOG_RECORD(eWarnType, "Invalid data detected at channel %zu", i);
            return false;
        }
    }

    return true;
}

bool TransientAnalyzer::DetectTransient(const std::vector<float>& data, int64_t timestamp)
{
    // 简单的幅值检测算法
    for (size_t i = 0; i < data.size(); ++i) {
        if (std::abs(data[i]) > m_config.trigger_threshold) {
            // 检查冷却时间
            if (timestamp - m_lastTriggerTime > m_triggerCooldown) {
                IEC_LOG_RECORD(eRunType, "Transient detected on channel %zu: value=%.3f",
                               i, data[i]);
                return true;
            }
        }
    }

    return false;
}

void TransientAnalyzer::TriggerRecording(const std::vector<float>& data, int64_t timestamp)
{
    m_lastTriggerTime = timestamp;

    // 创建COMTRADE配置
    ComtradeConfig config = CreateComtradeConfig(timestamp);

    // 生成文件名
    std::string filename = GenerateFilename(timestamp);

    // 写入配置文件
    if (ComtradeWriter::write_cfg(filename + ".cfg", config)) {
        IEC_LOG_RECORD(eRunType, "COMTRADE config file created: %s.cfg", filename.c_str());
    } else {
        IEC_LOG_RECORD(eErrType, "Failed to create COMTRADE config file: %s.cfg", filename.c_str());
    }

    // 准备采样数据
    std::vector<SamplePoint> samples = PrepareRecordingData(timestamp);

    // 写入数据文件
    if (ComtradeWriter::write_dat_ascii(filename + ".dat", config, samples)) {
        IEC_LOG_RECORD(eRunType, "COMTRADE data file created: %s.dat", filename.c_str());
    } else {
        IEC_LOG_RECORD(eErrType, "Failed to create COMTRADE data file: %s.dat", filename.c_str());
    }
}

ComtradeConfig TransientAnalyzer::CreateComtradeConfig(int64_t timestamp)
{
    ComtradeConfig config;

    config.station_name = "DTAnalyzer_Station";
    config.recorder_id = "DTAnalyzer_001";
    config.frequency = 50.0;

    // 创建模拟量通道
    for (int i = 0; i < m_config.channel_count; ++i) {
        AnalogChannel channel;
        channel.index = i + 1;
        channel.name = "CH" + std::to_string(i + 1);
        channel.phase = (i < 3) ? std::string(1, 'A' + i) : "N";
        channel.element = "V";
        channel.unit = "V";
        channel.factor_a = 1.0;
        channel.factor_b = 0.0;
        channel.offset_time = 0;
        channel.min_sample = -32768;
        channel.max_sample = 32767;

        config.analog_channels.push_back(channel);
    }

    // 设置采样率
    SamplingRate rate;
    rate.rate = m_config.sampling_rate;
    rate.points = m_config.pre_trigger_samples + m_config.post_trigger_samples;
    config.sampling_rates.push_back(rate);

    // 设置时间戳
    config.start_time = FormatTimestamp(timestamp - m_config.pre_trigger_samples * 1000000 / m_config.sampling_rate);
    config.end_time = FormatTimestamp(timestamp + m_config.post_trigger_samples * 1000000 / m_config.sampling_rate);

    return config;
}

std::vector<SamplePoint> TransientAnalyzer::PrepareRecordingData(int64_t timestamp)
{
    std::vector<SamplePoint> samples;

    size_t total_samples = m_config.pre_trigger_samples + m_config.post_trigger_samples;
    samples.reserve(total_samples);

    for (size_t i = 0; i < total_samples; ++i) {
        SamplePoint sample;
        sample.index = static_cast<int>(i + 1);
        sample.time = static_cast<int64_t>(i * 1000000 / m_config.sampling_rate);

        // 从缓冲区获取数据（这里简化处理，实际应该从环形缓冲区获取）
        sample.analog_values.resize(m_config.channel_count);
        for (int ch = 0; ch < m_config.channel_count; ++ch) {
            // 模拟数据，实际应该从数据缓冲区获取
            sample.analog_values[ch] = static_cast<int>(1000 * sin(2 * M_PI * 50 * i / m_config.sampling_rate));
        }

        samples.push_back(sample);
    }

    return samples;
}

std::string TransientAnalyzer::GenerateFilename(int64_t timestamp)
{
    time_t t = timestamp / 1000000;
    struct tm* tm_info = localtime(&t);

    char buffer[256];
    snprintf(buffer, sizeof(buffer), "%s/TRANS_%04d%02d%02d_%02d%02d%02d",
             COMTRADE_PATH,
             tm_info->tm_year + 1900,
             tm_info->tm_mon + 1,
             tm_info->tm_mday,
             tm_info->tm_hour,
             tm_info->tm_min,
             tm_info->tm_sec);

    return std::string(buffer);
}

std::string TransientAnalyzer::FormatTimestamp(int64_t timestamp)
{
    time_t t = timestamp / 1000000;
    int microseconds = timestamp % 1000000;
    struct tm* tm_info = localtime(&t);

    char buffer[64];
    snprintf(buffer, sizeof(buffer), "%02d/%02d/%02d,%02d:%02d:%02d.%06d",
             tm_info->tm_mon + 1,
             tm_info->tm_mday,
             tm_info->tm_year % 100,
             tm_info->tm_hour,
             tm_info->tm_min,
             tm_info->tm_sec,
             microseconds);

    return std::string(buffer);
}

//=============================================================================
// ComtradeWriter 类实现（保持原有功能）
//=============================================================================

bool ComtradeWriter::write_cfg(const std::string& filename, const ComtradeConfig& config) {
    try {
        std::ofstream file(filename);
        if (!file.is_open()) {
            return false;
        }
        
        // 写入厂站名称和录波器编号
        file << config.station_name << "," << config.recorder_id << "\n";
        
        // 写入通道数量信息
        file << config.total_channels() << "," 
             << config.analog_count() << "A," 
             << config.digital_count() << "D\n";
        
        // 写入模拟量通道信息
        for (const auto& analog : config.analog_channels) {
            file << analog.index << ","
                 << analog.name << ","
                 << analog.phase << ","
                 << analog.element << ","
                 << analog.unit << ","
                 << std::fixed << std::setprecision(6) << analog.factor_a << ","
                 << std::fixed << std::setprecision(6) << analog.factor_b << ","
                 << analog.offset_time << ","
                 << analog.min_sample << ","
                 << analog.max_sample << "\n";
        }
        
        // 写入数字量通道信息
        for (const auto& digital : config.digital_channels) {
            file << digital.index << ","
                 << digital.name << ","
                 << digital.state << "\n";
        }
        
        // 写入频率信息
        file << std::fixed << std::setprecision(1) << config.frequency << "\n";
        
        // 写入采样率数量
        file << config.sampling_rates.size() << "\n";

        
        
        // 写入各采样率信息
        for (const auto& rate : config.sampling_rates) {
            file << std::fixed << std::setprecision(1) << rate.rate << ","
                 << rate.points << "\n";
        }
        
        // 写入开始时间和结束时间
        file << config.start_time << "\n";
        file << config.end_time << "\n";

        
        
        // 写入文件类型(固定为ASCII)
        file << "ASCII\n";
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool ComtradeWriter::write_sample_ascii(std::ofstream& file, const SamplePoint& sample) {
    if (!file.is_open()) {
        return false;
    }
    
    try {
        // 写入序号和时间
        file << sample.index << "," << sample.time;
        
        // 写入模拟量数据
        for (int value : sample.analog_values) {
            file << "," << value;
        }
        
        // 写入数字量数据
        for (int value : sample.digital_values) {
            file << "," << value;
        }
        
        file << "\n";
        return true;
    }
    catch (...) {
        return false;
    }
}

bool ComtradeWriter::write_dat_ascii(const std::string& filename, const ComtradeConfig& config, 
                                    const std::vector<SamplePoint>& samples) {
    try {
        std::ofstream file(filename);
        if (!file.is_open()) {
            return false;
        }
        
        // 验证采样点数据与配置是否匹配
        for (const auto& sample : samples) {
            if (sample.analog_values.size() != config.analog_count() ||
                sample.digital_values.size() != config.digital_count()) {
                return false; // 采样点数据与配置的通道数量不匹配
            }
        }
        
        // 写入所有采样点
        for (const auto& sample : samples) {
            if (!write_sample_ascii(file, sample)) {
                return false;
            }
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}
    