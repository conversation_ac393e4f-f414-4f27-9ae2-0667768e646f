/*=====================================================================
 * 文件：transient_test.cpp
 *
 * 描述：TransientAnalyzer 三相电压电流测试程序
 *
 * 作者：系统生成			2025年1月
 * 
 * 修改记录：
 =====================================================================*/

#include "transient.h"
#include "public_struct.h"
#include <iostream>
#include <vector>
#include <chrono>
#include <thread>
#include <cmath>

/**
 * 测试三相电压电流数据生成
 */
std::vector<float> GenerateThreePhaseData(int sample_index, double sampling_rate)
{
    std::vector<float> data(TransientConfig::CHANNEL_COUNT);
    
    double t = sample_index / sampling_rate;  // 时间
    
    // 生成三相电压数据 (380V线电压，相位差120度)
    for (int i = 0; i < 3; ++i) {
        double phase_offset = i * 2.0 * M_PI / 3.0;  // 120度相位差
        data[i] = 380.0 * sqrt(2) * sin(2 * M_PI * 50 * t + phase_offset);
    }
    
    // 生成三相电流数据 (100A，相位差120度，滞后电压30度)
    for (int i = 3; i < 6; ++i) {
        double phase_offset = (i - 3) * 2.0 * M_PI / 3.0 - M_PI / 6.0;  // 120度相位差 + 30度滞后
        data[i] = 100.0 * sqrt(2) * sin(2 * M_PI * 50 * t + phase_offset);
    }
    
    return data;
}

/**
 * 测试暂态事件注入
 */
void InjectTransientEvent(std::vector<float>& data, int sample_index, int event_type)
{
    switch (event_type) {
        case 1:  // A相电压暂态
            data[0] += 600.0;  // UA增加600V
            std::cout << "注入A相电压暂态事件: UA = " << data[0] << "V" << std::endl;
            break;
            
        case 2:  // B相电流暂态
            data[4] += 200.0;  // IB增加200A
            std::cout << "注入B相电流暂态事件: IB = " << data[4] << "A" << std::endl;
            break;
            
        case 3:  // 三相电压同时暂态
            data[0] += 500.0;  // UA
            data[1] += 500.0;  // UB
            data[2] += 500.0;  // UC
            std::cout << "注入三相电压暂态事件" << std::endl;
            break;
            
        default:
            break;
    }
}

/**
 * 打印通道信息
 */
void PrintChannelInfo()
{
    std::cout << "\n=== 通道配置信息 ===" << std::endl;
    std::cout << "总通道数: " << TransientConfig::CHANNEL_COUNT << std::endl;
    
    for (int i = 0; i < TransientConfig::CHANNEL_COUNT; ++i) {
        std::cout << "通道 " << i << ": " 
                  << TransientConfig::CHANNEL_NAMES[i] << " ("
                  << TransientConfig::CHANNEL_PHASES[i] << "相) - "
                  << TransientConfig::CHANNEL_UNITS[i] << std::endl;
    }
    std::cout << "===================" << std::endl;
}

/**
 * 主测试函数
 */
int main()
{
    std::cout << "=== TransientAnalyzer 三相电压电流测试 ===" << std::endl;
    
    // 打印通道信息
    PrintChannelInfo();
    
    // 创建暂态分析器
    TransientAnalyzer analyzer;
    
    // 初始化
    if (!analyzer.Init()) {
        std::cerr << "暂态分析器初始化失败!" << std::endl;
        return -1;
    }
    
    // 启动
    if (!analyzer.Start()) {
        std::cerr << "暂态分析器启动失败!" << std::endl;
        return -1;
    }
    
    std::cout << "\n开始数据处理测试..." << std::endl;
    
    const double sampling_rate = 12800.0;  // 采样率
    const int total_samples = 2000;        // 总采样点数
    
    for (int i = 0; i < total_samples; ++i) {
        // 生成三相电压电流数据
        std::vector<float> data = GenerateThreePhaseData(i, sampling_rate);
        
        // 在特定时刻注入暂态事件
        if (i == 500) {
            InjectTransientEvent(data, i, 1);  // A相电压暂态
        } else if (i == 1000) {
            InjectTransientEvent(data, i, 2);  // B相电流暂态
        } else if (i == 1500) {
            InjectTransientEvent(data, i, 3);  // 三相电压暂态
        }
        
        // 获取时间戳
        auto now = std::chrono::high_resolution_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::microseconds>(
            now.time_since_epoch()).count();
        
        // 处理数据
        analyzer.ProcessData(data, timestamp);
        
        // 模拟采样间隔
        std::this_thread::sleep_for(std::chrono::microseconds(78));  // 12.8kHz采样率
        
        // 每1000个采样点打印一次进度
        if (i % 1000 == 0) {
            std::cout << "处理进度: " << i << "/" << total_samples << " 采样点" << std::endl;
        }
    }
    
    std::cout << "\n数据处理完成!" << std::endl;
    
    // 停止分析器
    analyzer.Stop();
    
    std::cout << "=== 测试完成 ===" << std::endl;
    
    return 0;
}

/**
 * 编译命令示例:
 * g++ -std=c++11 -I../include -I../include/tzc_std -I../include/meg_pub \
 *     transient_test.cpp transient.cpp public_struct.cpp \
 *     -L../lib -ltzcstd -lmegpub -lpthread -lssl -lcrypto \
 *     -o transient_test
 */
