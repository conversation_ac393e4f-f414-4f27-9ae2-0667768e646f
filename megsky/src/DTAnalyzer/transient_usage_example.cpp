/*=====================================================================
 * 文件：transient_usage_example.cpp
 *
 * 描述：TransientAnalyzer 使用示例
 *
 * 作者：系统生成			2025年1月
 * 
 * 修改记录：
 =====================================================================*/

#include "transient.h"
#include "public_struct.h"
#include <vector>
#include <iostream>
#include <chrono>
#include <thread>

/**
 * 示例：如何在项目中使用 TransientAnalyzer
 */
void TransientAnalyzerUsageExample()
{
    // 1. 创建暂态分析器实例
    TransientAnalyzer analyzer;
    
    // 2. 初始化分析器
    if (!analyzer.Init()) {
        IEC_LOG_RECORD(eErrType, "Failed to initialize TransientAnalyzer");
        return;
    }
    
    // 3. 启动分析器
    if (!analyzer.Start()) {
        IEC_LOG_RECORD(eErrType, "Failed to start TransientAnalyzer");
        return;
    }
    
    // 4. 模拟实时数据处理
    std::vector<float> sampleData(8); // 8通道数据
    
    for (int i = 0; i < 1000; ++i) {
        // 模拟采样数据
        for (int ch = 0; ch < 8; ++ch) {
            // 正常数据
            sampleData[ch] = 220.0f * sin(2 * M_PI * 50 * i / 12800.0);
            
            // 在第500个采样点注入暂态
            if (i == 500 && ch == 0) {
                sampleData[ch] += 500.0f; // 注入暂态信号
            }
        }
        
        // 获取当前时间戳（微秒）
        auto now = std::chrono::high_resolution_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::microseconds>(
            now.time_since_epoch()).count();
        
        // 处理数据
        analyzer.ProcessData(sampleData, timestamp);
        
        // 模拟采样间隔 (12.8kHz采样率)
        std::this_thread::sleep_for(std::chrono::microseconds(78));
    }
    
    // 5. 停止分析器
    analyzer.Stop();
    
    IEC_LOG_RECORD(eRunType, "TransientAnalyzer example completed");
}

/**
 * 示例：如何在 CTaskManager 中集成 TransientAnalyzer
 */
class CTaskManagerWithTransient
{
private:
    TransientAnalyzer m_transientAnalyzer;
    
public:
    bool Init()
    {
        // 初始化暂态分析器
        if (!m_transientAnalyzer.Init()) {
            IEC_LOG_RECORD(eErrType, "Failed to initialize TransientAnalyzer in CTaskManager");
            return false;
        }
        
        // 启动暂态分析器
        if (!m_transientAnalyzer.Start()) {
            IEC_LOG_RECORD(eErrType, "Failed to start TransientAnalyzer in CTaskManager");
            return false;
        }
        
        IEC_LOG_RECORD(eRunType, "CTaskManager with TransientAnalyzer initialized successfully");
        return true;
    }
    
    void Exit()
    {
        m_transientAnalyzer.Stop();
        IEC_LOG_RECORD(eRunType, "CTaskManager with TransientAnalyzer exited");
    }
    
    void ProcessRealtimeData(const std::vector<float>& data, int64_t timestamp)
    {
        // 处理暂态分析
        if (m_transientAnalyzer.IsRunning()) {
            m_transientAnalyzer.ProcessData(data, timestamp);
        }
        
        // 其他实时数据处理...
    }
};

/**
 * 示例：如何创建自定义的暂态检测算法
 */
class CustomTransientAnalyzer : public TransientAnalyzer
{
public:
    // 可以重写检测算法，添加更复杂的暂态检测逻辑
    // 例如：基于小波变换、FFT分析等
    
    bool ProcessDataWithCustomAlgorithm(const std::vector<float>& data, int64_t timestamp)
    {
        // 自定义预处理
        std::vector<float> processedData = CustomPreprocess(data);
        
        // 调用基类的处理方法
        return ProcessData(processedData, timestamp);
    }
    
private:
    std::vector<float> CustomPreprocess(const std::vector<float>& data)
    {
        std::vector<float> result = data;
        
        // 添加滤波、去噪等预处理
        // 这里只是示例，实际可以添加更复杂的算法
        for (auto& value : result) {
            // 简单的低通滤波
            static float lastValue = 0.0f;
            value = 0.9f * lastValue + 0.1f * value;
            lastValue = value;
        }
        
        return result;
    }
};

/**
 * 主函数示例
 */
int main()
{
    IEC_LOG_RECORD(eRunType, "Starting TransientAnalyzer examples");
    
    // 运行基本使用示例
    TransientAnalyzerUsageExample();
    
    // 运行集成示例
    CTaskManagerWithTransient taskManager;
    if (taskManager.Init()) {
        // 模拟一些数据处理
        std::vector<float> testData(8, 220.0f);
        auto timestamp = std::chrono::duration_cast<std::chrono::microseconds>(
            std::chrono::high_resolution_clock::now().time_since_epoch()).count();
        
        taskManager.ProcessRealtimeData(testData, timestamp);
        taskManager.Exit();
    }
    
    IEC_LOG_RECORD(eRunType, "TransientAnalyzer examples completed");
    return 0;
}
